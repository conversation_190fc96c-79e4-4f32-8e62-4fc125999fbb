import { createSignal, createEffect, onMount, onCleanup } from "solid-js";
import { getRandomFact, type FunFact } from "@data/funFacts";

export default function PullToRevealWidget() {
  const [isRevealed, setIsRevealed] = createSignal(false);
  const [currentFact, setCurrentFact] = createSignal<FunFact>(getRandomFact());
  const [pullDistance, setPullDistance] = createSignal(0);
  const [isDragging, setIsDragging] = createSignal(false);
  const [showHint, setShowHint] = createSignal(true);

  let containerRef: HTMLDivElement | undefined;
  let startY = 0;
  let currentY = 0;
  const REVEAL_THRESHOLD = 60; // pixels to pull before revealing (reduced for easier mobile use)
  const MAX_PULL = 100; // maximum pull distance

  // Touch event handlers
  const handleTouchStart = (e: TouchEvent) => {
    if (isRevealed()) return;
    startY = e.touches[0].clientY;
    setIsDragging(true);
    setShowHint(false);
    e.preventDefault();
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging() || isRevealed()) return;

    currentY = e.touches[0].clientY;
    const deltaY = currentY - startY;

    if (deltaY > 0) {
      e.preventDefault();
      const distance = Math.min(deltaY, MAX_PULL);
      setPullDistance(distance);
    }
  };

  const handleTouchEnd = () => {
    if (!isDragging()) return;
    setIsDragging(false);

    if (pullDistance() >= REVEAL_THRESHOLD) {
      setIsRevealed(true);
      setPullDistance(0);
    } else {
      // Snap back animation
      setPullDistance(0);
    }
  };

  // Mouse event handlers for desktop
  const handleMouseDown = (e: MouseEvent) => {
    if (isRevealed()) return;
    startY = e.clientY;
    setIsDragging(true);
    setShowHint(false);
    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging() || isRevealed()) return;

    currentY = e.clientY;
    const deltaY = currentY - startY;

    if (deltaY > 0) {
      const distance = Math.min(deltaY, MAX_PULL);
      setPullDistance(distance);
    }
  };

  const handleMouseUp = () => {
    if (!isDragging()) return;
    setIsDragging(false);

    if (pullDistance() >= REVEAL_THRESHOLD) {
      setIsRevealed(true);
      setPullDistance(0);
    } else {
      setPullDistance(0);
    }
  };

  // Close the revealed content
  const handleClose = () => {
    setIsRevealed(false);
    setCurrentFact(getRandomFact()); // Get a new fact for next time
    setTimeout(() => setShowHint(true), 1000); // Show hint again after a delay
  };

  // Get a new fact
  const getNewFact = () => {
    setCurrentFact(getRandomFact());
  };

  // Keyboard support
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      if (!isRevealed()) {
        setIsRevealed(true);
        setShowHint(false);
      }
    }
    if (e.key === 'Escape' && isRevealed()) {
      handleClose();
    }
  };

  onMount(() => {
    // Add global mouse event listeners for desktop drag
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Hide hint after some time
    const hintTimer = setTimeout(() => setShowHint(false), 5000);

    onCleanup(() => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      clearTimeout(hintTimer);
    });
  });

  return (
    <div class="w-full max-w-md mx-auto px-4 sm:px-0 space-y-4 pb-8">
      {/* Pull Handle */}
      <div
        ref={containerRef}
        class="relative bg-white dark:bg-black border border-black/10 dark:border-white/20 rounded-xl p-6 cursor-grab active:cursor-grabbing transition-all duration-300 ease-out hover:border-black/20 dark:hover:border-white/30"
        style={{
          transform: `translateY(${pullDistance()}px)`,
          "box-shadow": pullDistance() > 0
            ? `0 ${pullDistance() / 4}px ${pullDistance() / 2}px rgba(0,0,0,0.1)`
            : undefined
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        onKeyDown={handleKeyDown}
        role="button"
        tabindex="0"
        aria-label="Pull down to reveal a fun fact"
      >
        {/* Pull Indicator */}
        <div class="flex flex-col items-center space-y-3">
          <div class="flex space-x-1">
            <div class="w-8 h-1 bg-black/20 dark:bg-white/20 rounded-full"></div>
            <div class="w-8 h-1 bg-black/20 dark:bg-white/20 rounded-full"></div>
            <div class="w-8 h-1 bg-black/20 dark:bg-white/20 rounded-full"></div>
          </div>

          <div class="text-center">
            <h3 class="font-semibold text-black dark:text-white mb-2">
              🎲 Fun Facts
            </h3>
            <p class="text-sm text-black/60 dark:text-white/60">
              Pull down to reveal a random fact!
            </p>
          </div>

          {/* Animated Arrow */}
          <div
            class={`transition-all duration-300 ${isDragging() ? 'scale-110' : ''}`}
            style={{
              transform: `rotate(${Math.min(pullDistance() * 2, 180)}deg)`
            }}
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="text-black/40 dark:text-white/40"
            >
              <path d="M12 5v14M19 12l-7 7-7-7" />
            </svg>
          </div>

          {/* Pull Progress Indicator */}
          {pullDistance() > 0 && (
            <div class="w-full bg-black/10 dark:bg-white/10 rounded-full h-2 overflow-hidden">
              <div
                class="h-full rounded-full transition-all duration-100"
                classList={{
                  "bg-green-500": pullDistance() >= REVEAL_THRESHOLD,
                  "bg-black/30 dark:bg-white/30": pullDistance() < REVEAL_THRESHOLD
                }}
                style={{
                  width: `${Math.min((pullDistance() / REVEAL_THRESHOLD) * 100, 100)}%`
                }}
              ></div>
            </div>
          )}

          {/* Hint Animation */}
          {showHint() && !isDragging() && !isRevealed() && (
            <div class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 animate-pull-bounce">
              <div class="text-xs text-black/40 dark:text-white/40 whitespace-nowrap bg-white dark:bg-black px-2 py-1 rounded-full border border-black/10 dark:border-white/20">
                👆 Try pulling me down
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Revealed Content */}
      {isRevealed() && (
        <div class="bg-white dark:bg-black border border-black/10 dark:border-white/20 rounded-xl p-6 animate-reveal-slide shadow-lg overflow-hidden">
          <div class="flex justify-between items-start mb-4">
            <div class="flex items-center space-x-2">
              <span class="text-2xl">{currentFact().icon}</span>
              <span class="text-xs uppercase font-medium px-2 py-1 rounded-full bg-black/5 dark:bg-white/10 text-black/60 dark:text-white/60">
                {currentFact().category}
              </span>
            </div>
            <button
              onClick={handleClose}
              class="text-black/40 dark:text-white/40 hover:text-black dark:hover:text-white transition-colors"
              aria-label="Close"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <p class="text-black dark:text-white mb-4 leading-relaxed">
            {currentFact().text}
          </p>

          <button
            onClick={getNewFact}
            class="w-full py-2 px-4 bg-black dark:bg-white text-white dark:text-black rounded-lg hover:opacity-75 transition-opacity font-medium text-sm"
          >
            🎲 Get Another Fact
          </button>
        </div>
      )}
    </div>
  );
}
