export interface Fact {
  id: string;
  category: "tech" | "programming" | "personal" | "industry";
  text: string;
  source?: string;
}

export const facts: Fact[] = [
  {
    id: "1",
    category: "programming",
    text: "The first computer bug was an actual bug - a moth found trapped in a Harvard Mark II computer in 1947.",
    source: "Computer History",
  },
  {
    id: "2",
    category: "tech",
    text: "JavaScript was created in just 10 days by <PERSON> at Netscape in 1995.",
    source: "Programming History",
  },
  {
    id: "3",
    category: "programming",
    text: 'The term "debugging" was coined by <PERSON> <PERSON> when she found the moth in the computer.',
    source: "Computing Etymology",
  },
  {
    id: "4",
    category: "tech",
    text: "The first 1GB hard drive, IBM 3380, was released in 1980 and cost $40,000.",
    source: "Tech History",
  },
  {
    id: "5",
    category: "programming",
    text: "Python was named after the British comedy group Monty Python, not the snake.",
    source: "Programming Trivia",
  },
  {
    id: "6",
    category: "industry",
    text: "The average developer spends 75% of their time debugging and only 25% writing new code.",
    source: "Developer Statistics",
  },
  {
    id: "7",
    category: "tech",
    text: "The first computer mouse was made of wood and had only one button.",
    source: "Hardware History",
  },
  {
    id: "8",
    category: "programming",
    text: "The first programming language was Fortran, created in 1957 by IBM.",
    source: "Programming History",
  },
  {
    id: "9",
    category: "personal",
    text: "I started coding when I was in high school and fell in love with problem-solving through code.",
    source: "Personal Journey",
  },
  {
    id: "10",
    category: "tech",
    text: 'The "@" symbol was used in email addresses because it was the only preposition available on the keyboard.',
    source: "Internet History",
  },
  {
    id: "11",
    category: "programming",
    text: "The first version of Git was written by Linus Torvalds in just 2 weeks.",
    source: "Version Control History",
  },
  {
    id: "12",
    category: "industry",
    text: "Stack Overflow was launched in 2008 and now has over 50 million monthly visitors.",
    source: "Developer Community",
  },
  {
    id: "13",
    category: "personal",
    text: "My favorite programming language changes every few months as I discover new paradigms and tools.",
    source: "Personal Preference",
  },
  {
    id: "14",
    category: "tech",
    text: "The first website ever created is still online: info.cern.ch",
    source: "Web History",
  },
  {
    id: "15",
    category: "programming",
    text: "The most expensive software bug in history cost $1.2 billion when NASA lost the Mars Climate Orbiter.",
    source: "Software Engineering",
  },
  {
    id: "16",
    category: "tech",
    text: "The first computer programmer was Ada Lovelace, who wrote the first algorithm in 1843.",
    source: "Computing History",
  },
  {
    id: "17",
    category: "programming",
    text: 'The term "Hello, World!" was first used in a 1978 book called "The C Programming Language".',
    source: "Programming Tradition",
  },
  {
    id: "18",
    category: "personal",
    text: "I believe the best code is not just functional, but also readable and maintainable for future developers.",
    source: "Personal Philosophy",
  },
  {
    id: "19",
    category: "industry",
    text: "There are more possible games of chess than atoms in the observable universe.",
    source: "Computer Science",
  },
  {
    id: "20",
    category: "tech",
    text: 'The first computer virus was created in 1971 and was called "The Creeper".',
    source: "Cybersecurity History",
  },
];

export function getRandomFact(): Fact {
  const randomIndex = Math.floor(Math.random() * facts.length);
  return facts[randomIndex];
}

export function getFactsByCategory(category: Fact["category"]): Fact[] {
  return facts.filter((fact) => fact.category === category);
}
