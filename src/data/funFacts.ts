export interface FunFact {
  id: number;
  text: string;
  category: "personal" | "tech" | "general";
  icon?: string;
}

export const funFacts: FunFact[] = [
  // Personal facts about Imad
  {
    id: 1,
    text: "I'm from Morocco, a country where you can ski in the mountains and surf at the beach on the same day! 🏔️🏄‍♂️",
    category: "personal",
    icon: "🇲🇦"
  },
  {
    id: 2,
    text: "I speak Arabic, French, and English - that's three different writing systems! 📝",
    category: "personal",
    icon: "🗣️"
  },
  {
    id: 3,
    text: "I'm studying at ENSET Mohammedia, one of Morocco's top engineering schools 🎓",
    category: "personal",
    icon: "🏫"
  },
  {
    id: 4,
    text: "I've worked with both <PERSON><PERSON><PERSON> and React Native - I love cross-platform development! 📱",
    category: "personal",
    icon: "💻"
  },
  {
    id: 5,
    text: "I started coding during my university studies and fell in love with problem-solving 💡",
    category: "personal",
    icon: "❤️"
  },

  // Tech facts
  {
    id: 6,
    text: "The first computer bug was an actual bug - a moth found in a Harvard computer in 1947! 🐛",
    category: "tech",
    icon: "🐛"
  },
  {
    id: 7,
    text: "JavaScript was created in just 10 days by <PERSON> in 1995 ⚡",
    category: "tech",
    icon: "⚡"
  },
  {
    id: 8,
    text: "The term 'debugging' was coined by Admiral <PERSON> 🚢",
    category: "tech",
    icon: "🔧"
  },
  {
    id: 9,
    text: "Python is named after Monty Python's Flying Circus, not the snake! 🐍",
    category: "tech",
    icon: "🐍"
  },
  {
    id: 10,
    text: "The first 1GB hard drive cost $40,000 in 1980 - that's $140,000 today! 💾",
    category: "tech",
    icon: "💾"
  },
  {
    id: 11,
    text: "React was created by Facebook and is now used by Netflix, Airbnb, and Instagram 📱",
    category: "tech",
    icon: "⚛️"
  },
  {
    id: 12,
    text: "TypeScript was developed by Microsoft to add static typing to JavaScript 📝",
    category: "tech",
    icon: "📘"
  },

  // General interesting facts
  {
    id: 13,
    text: "Honey never spoils - archaeologists have found edible honey in ancient Egyptian tombs! 🍯",
    category: "general",
    icon: "🍯"
  },
  {
    id: 14,
    text: "Octopuses have three hearts and blue blood 🐙",
    category: "general",
    icon: "🐙"
  },
  {
    id: 15,
    text: "A group of flamingos is called a 'flamboyance' 🦩",
    category: "general",
    icon: "🦩"
  },
  {
    id: 16,
    text: "Bananas are berries, but strawberries aren't! 🍌",
    category: "general",
    icon: "🍌"
  },
  {
    id: 17,
    text: "The human brain uses about 20% of the body's total energy ⚡",
    category: "general",
    icon: "🧠"
  },
  {
    id: 18,
    text: "There are more possible games of chess than atoms in the observable universe ♟️",
    category: "general",
    icon: "♟️"
  },
  {
    id: 19,
    text: "A day on Venus is longer than its year! 🪐",
    category: "general",
    icon: "🪐"
  },
  {
    id: 20,
    text: "Dolphins have names for each other - they use unique whistle signatures 🐬",
    category: "general",
    icon: "🐬"
  }
];

export const getRandomFact = (): FunFact => {
  const randomIndex = Math.floor(Math.random() * funFacts.length);
  return funFacts[randomIndex];
};

export const getFactsByCategory = (category: FunFact['category']): FunFact[] => {
  return funFacts.filter(fact => fact.category === category);
};
